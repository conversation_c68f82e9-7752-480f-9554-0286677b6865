<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Arr;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\Refcodes;
use app\back\repositories\VipaffRefcodes;

class VipaffRefcodesTask extends ImportTask
{
    use TaskWithFromToRequest;

    private array $refcodesWebmasters = [];

    public function __construct(
        private readonly Refcodes $refcodesRepo,
        private readonly VipaffRefcodes $vipaffRefcodesRepo,
    ) {
    }

    public function process(): void
    {
        parent::process();
        $this->batchUpdateWebmasters();
    }

    protected function beforeFind(array &$row): bool
    {
        $row['refcode_id'] = $this->refcodesRepo->getIdByCode($row['ref_code']);

        unset($row['ref_code']);

        if (array_key_exists('aff_source', $row) && $row['aff_source'] === '') {
            $row['aff_source'] = null;
        }

        if (!empty($row['webmaster_id'])) {
            $this->refcodesWebmasters[] = [
                'id' => $row['refcode_id'],
                'webmaster_id' => $row['webmaster_id'],
            ];
        }

        return parent::beforeFind($row);
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['refcode_id']); // Duplicates for different sites



        return parent::batchUpsert($repository, $rows);
    }

    protected function repository(): BaseRepository
    {
        return $this->vipaffRefcodesRepo;
    }

    private function batchUpdateWebmasters(): void
    {
        if (empty($this->refcodesWebmasters)) {
            return;
        }

        $this->affectedRows += $this->refcodesRepo->batchUpdateDistinct($refcodesWebmasters, ['updated_at' => 'NOW()']);
    }
}
