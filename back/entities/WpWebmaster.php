<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\CallableValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\enums\WpAffOwner;
use app\back\entities\enums\WpLocation;

class WpWebmaster extends BaseEntity
{
    public const array FORBIDDEN_PREFIXES = [Refcode::VIP_AFF_PREFIXES, Refcode::SLOTTY_AFF_PREFIX];

    #[IntValidator]
    public int $id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $registered_at;
    #[StringValidator(1, 50)]
    #[CallableValidator([self::class, 'validateAffOwner'])]
    public ?string $aff_owner;
    #[StringValidator(1, 50)]
    #[CallableValidator([self::class, 'validateLocation'])]
    public ?string $location;

    public static function validateLocation(?string $value): ?string
    {
        WpLocation::check($value);
        return null;
    }

    public static function validateAffOwner(?string $value): ?string
    {
        WpAffOwner::check($value);
        return null;
    }
}
